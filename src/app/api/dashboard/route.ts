import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export const dynamic = 'force-dynamic'

// GET /api/dashboard - 获取仪表板数据
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const userId = session.user.id
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    // 检查用户角色
    const currentUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    })

    const isAdmin = currentUser?.role === 'admin'

    // 基础统计数据
    let stats: any = {}

    if (isAdmin) {
      // 管理员可以看到全部统计
      const [
        totalUsers,
        activeUsers,
        totalForms,
        activeForms,
        totalRecords,
        todayRecords,
        webhookSuccess,
        webhookErrors,
      ] = await Promise.all([
        // 用户统计
        prisma.user.count(),
        prisma.user.count({ where: { is_active: true } }),

        // 表单统计
        prisma.formConfig.count(),
        prisma.formConfig.count({ where: { isActive: true } }),

        // 数据统计 - 需要动态查询所有表
        getTotalRecords(),
        getTodayRecords(today, tomorrow),

        // Webhook统计
        prisma.systemLog.count({
          where: {
            action: 'WEBHOOK_RECEIVED',
            createdAt: { gte: today },
          },
        }),
        prisma.systemLog.count({
          where: {
            action: 'WEBHOOK_ERROR',
            createdAt: { gte: today },
          },
        }),
      ])

      stats = {
        totalUsers,
        activeUsers,
        totalForms,
        activeForms,
        totalRecords,
        todayRecords,
        webhookSuccess,
        webhookErrors,
      }
    } else {
      // 普通用户只能看到数据统计
      const [totalRecords, todayRecords] = await Promise.all([
        getTotalRecords(),
        getTodayRecords(today, tomorrow),
      ])

      stats = {
        totalUsers: 0,
        activeUsers: 0,
        totalForms: 0,
        activeForms: 0,
        totalRecords,
        todayRecords,
        webhookSuccess: 0,
        webhookErrors: 0,
      }
    }

    // 获取最近活动（最多10条）
    const recentActivities = await prisma.systemLog.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      where: isAdmin ? {} : { userId }, // 普通用户只能看到自己的活动
      include: {
        user: {
          select: {
            username: true,
            doctor_name: true,
          },
        },
      },
    })

    // 获取表单统计（仅管理员）
    let formStats: any[] = []
    if (isAdmin) {
      const forms = await prisma.formConfig.findMany({
        where: { isActive: true },
        select: {
          formId: true,
          formName: true,
          tableName: true,
          isActive: true,
        },
      })

      // 获取每个表单的数据统计
      formStats = await Promise.all(
        forms.map(async form => {
          const [recordCount, todayCount] = await Promise.all([
            getTableRecordCount(form.tableName!),
            getTableTodayCount(form.tableName!, today, tomorrow),
          ])

          return {
            formId: form.formId,
            formName: form.formName,
            recordCount,
            todayCount,
            isActive: form.isActive,
          }
        })
      )

      // 按记录数量排序
      formStats.sort((a, b) => b.recordCount - a.recordCount)
    }

    return NextResponse.json({
      success: true,
      data: {
        stats,
        recentActivities: recentActivities.map(log => ({
          ...log,
          id: log.id.toString(),
        })),
        formStats,
        isAdmin,
      },
    })
  } catch (error) {
    console.error('获取仪表板数据失败:', error)
    return NextResponse.json(
      { success: false, error: '获取仪表板数据失败' },
      { status: 500 }
    )
  }
}

// 获取所有数据表的总记录数
async function getTotalRecords(): Promise<number> {
  try {
    const forms = await prisma.formConfig.findMany({
      where: { isActive: true },
      select: { tableName: true },
    })

    let total = 0
    for (const form of forms) {
      if (form.tableName) {
        const count = await getTableRecordCount(form.tableName)
        total += count
      }
    }

    return total
  } catch (error) {
    console.error('获取总记录数失败:', error)
    return 0
  }
}

// 获取今日新增记录数
async function getTodayRecords(today: Date, tomorrow: Date): Promise<number> {
  try {
    const forms = await prisma.formConfig.findMany({
      where: { isActive: true },
      select: { tableName: true },
    })

    let total = 0
    for (const form of forms) {
      if (form.tableName) {
        const count = await getTableTodayCount(form.tableName, today, tomorrow)
        total += count
      }
    }

    return total
  } catch (error) {
    console.error('获取今日记录数失败:', error)
    return 0
  }
}

// 获取单个表的记录数
async function getTableRecordCount(tableName: string): Promise<number> {
  try {
    const result = await prisma.$queryRawUnsafe<Array<{ count: number }>>(
      `SELECT COUNT(*) as count FROM \`${tableName}\``
    )
    return result[0]?.count || 0
  } catch (error) {
    console.error(`获取表 ${tableName} 记录数失败:`, error)
    return 0
  }
}

// 获取单个表的今日记录数
async function getTableTodayCount(
  tableName: string,
  today: Date,
  tomorrow: Date
): Promise<number> {
  try {
    const result = await prisma.$queryRawUnsafe<Array<{ count: number }>>(
      `SELECT COUNT(*) as count FROM \`${tableName}\` WHERE created_at >= ? AND created_at < ?`,
      today,
      tomorrow
    )
    return result[0]?.count || 0
  } catch (error) {
    console.error(`获取表 ${tableName} 今日记录数失败:`, error)
    return 0
  }
}
