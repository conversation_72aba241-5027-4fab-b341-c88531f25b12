/**
 * PM2 配置文件 - 肺功能数据管理平台
 * 
 * 使用方法：
 * - 开发环境：pm2 start ecosystem.config.js --env development
 * - 生产环境：pm2 start ecosystem.config.js --env production
 * - 集群模式：pm2 start ecosystem.config.js --env production-cluster
 */

module.exports = {
  apps: [
    {
      // ==================== 基础配置 ====================
      name: 'lung-function-admin',
      script: 'yarn',
      args: 'start',
      cwd: process.cwd(),
      
      // ==================== 环境文件配置 ====================
      // 关键：使用 node_args 加载环境文件（推荐方式）
      node_args: '--env-file=.env.production',
      
      // ==================== 进程管理配置 ====================
      instances: 1,                    // 实例数量，可设为 'max' 使用所有CPU核心
      exec_mode: 'fork',               // 执行模式：'fork' 或 'cluster'
      autorestart: true,               // 自动重启
      watch: false,                    // 文件监听（生产环境建议关闭）
      max_memory_restart: '1G',        // 内存限制重启
      min_uptime: '10s',               // 最小运行时间
      max_restarts: 10,                // 最大重启次数
      restart_delay: 4000,             // 重启延迟
      
      // ==================== 环境变量配置 ====================
      // 生产环境变量（会覆盖 .env.production 中的同名变量）
      env_production: {
        NODE_ENV: 'production',
        PORT: 3011,
        PM2_SERVE_PATH: '.',
        PM2_SERVE_PORT: 3011,
        // 在这里可以覆盖关键的生产环境配置
        // DATABASE_URL: 'mysql://...',  // 如果需要覆盖数据库连接
        // NEXTAUTH_URL: 'https://yourdomain.com',
      },
      
      // 开发环境变量
      env_development: {
        NODE_ENV: 'development',
        PORT: 3011,
        LOG_LEVEL: 'debug',
      },
      
      // 集群生产环境配置
      env_production_cluster: {
        NODE_ENV: 'production',
        PORT: 3011,
        instances: 'max',
        exec_mode: 'cluster',
      },
      
      // ==================== 日志配置 ====================
      error_file: './logs/pm2-error.log',
      out_file: './logs/pm2-out.log',
      log_file: './logs/pm2-combined.log',
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      log_type: 'json',                // 结构化日志
      
      // 日志轮转配置
      log_file_max_size: '10M',
      log_file_max_files: 5,
      
      // ==================== 进程控制 ====================
      kill_timeout: 5000,             // 强制杀死超时
      wait_ready: true,                // 等待应用就绪
      listen_timeout: 10000,           // 监听超时
      shutdown_with_message: true,     // 优雅关闭
      
      // ==================== 健康检查 ====================
      // 注意：需要应用提供健康检查端点
      health_check_url: 'http://localhost:3011/api/health',
      health_check_grace_period: 3000,
      
      // ==================== 高级配置 ====================
      source_map_support: true,       // 源码映射支持
      disable_source_map_support: false,
      
      // 监控配置
      pmx: true,                       // 启用PMX监控
      automation: false,               // 禁用自动化
      
      // 忽略监听的文件/目录（当 watch: true 时生效）
      ignore_watch: [
        'node_modules',
        'logs',
        '.next',
        '.git',
        'public/uploads',
        '*.log',
        '.env*'
      ],
      
      // ==================== 性能优化 ====================
      // Node.js 性能选项
      node_args: [
        '--env-file=.env.production',
        '--max-old-space-size=1024',   // 最大堆内存
        '--optimize-for-size',         // 优化内存使用
      ].join(' '),
      
      // ==================== 错误处理 ====================
      // 异常重启配置
      exp_backoff_restart_delay: 100,  // 指数退避重启延迟
      
      // ==================== 元数据 ====================
      vizion: true,                    // 版本控制信息
      post_update: ['yarn install'],  // 更新后执行的命令
      
      // 自定义环境变量
      env: {
        COMMON_VARIABLE: 'true',
        PM2_APP_NAME: 'lung-function-admin',
      }
    }
  ],
  
  // ==================== 部署配置 ====================
  deploy: {
    // 生产环境部署配置
    production: {
      user: 'lungapp',
      host: ['your-server-ip'],
      ref: 'origin/main',
      repo: 'https://github.com/peckbyte/free_lung_function_project_admin.git',
      path: '/home/<USER>/www/wwwroot/free_lung_function_project',
      'post-deploy': [
        'yarn install',
        'npx prisma generate',
        'npx prisma db push',
        'yarn build',
        'pm2 reload ecosystem.config.js --env production'
      ].join(' && '),
      'pre-setup': 'apt update && apt install git -y',
      'post-setup': [
        'ls -la',
        'yarn install',
        'yarn build'
      ].join(' && '),
      ssh_options: 'StrictHostKeyChecking=no',
      env: {
        NODE_ENV: 'production'
      }
    },
    
    // 开发环境部署配置
    development: {
      user: 'dev',
      host: 'dev-server-ip',
      ref: 'origin/develop',
      repo: 'https://github.com/peckbyte/free_lung_function_project_admin.git',
      path: '/home/<USER>/www/wwwroot/free_lung_function_project',
      'post-deploy': [
        'yarn install',
        'npx prisma generate',
        'yarn build',
        'pm2 reload ecosystem.config.js --env development'
      ].join(' && '),
      env: {
        NODE_ENV: 'development'
      }
    }
  }
};
